from fastapi import APIRouter, Depends, HTTPException, UploadFile, File,status
from sqlalchemy.orm import Session
from typing import List
import os
import uuid
from app.db import crud
from app.db.session import get_db
from app.schemas.properties import PropertyCreate, PropertyUpdate, PropertyOut
from app.core.security import get_current_active_user
from  app.core.fraud_detection import check_property_fraud


router = APIRouter()

@router.post("/", response_model=PropertyOut)
async def create_property(
    property: PropertyCreate,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_active_user)
):
    if not current_user.kyc_verified: # type: ignore
        raise HTTPException(
            status_code = status.HTTP_403_FORBIDDEN,
            detail = "KYC verification is required to create a property"
        )
        
    fraud_check = await check_property_fraud(property_data.dict()) # type: ignore
    if fraud_check:
        raise HTTPException(
            status_code = status.HTTP_400_BAD_REQUEST,
            detail = fraud_check.message
        )
    return crud.create_property(
        db=db,
        property_data=property_data.dict(), # type: ignore
        owner_id=current_user.id # type: ignore
        )
   
   
@router.get("/{property_id}", response_model=PropertyOut)
async def list_property(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    return crud.get_properties(db, skip=skip, limit=limit)

@router.get("/{property_id}", response_model=PropertyOut)
async def get_property(
    property_id: int,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_active_user)
):
    property = crud.get_property(db, property_id=property_id)
    if not property:
        raise HTTPException(
            status_code = status.HTTP_404_NOT_FOUND,
            detail = "Property not found"
        )
    if current_user:
        crud.add_recently_viewed(db, user_id=current_user.id, property_id=property_id) # type: ignore
    
    return property

@router.post("/{property_id}/images")
async def upload_property_images(
    property_id: int,
    images: List[UploadFile] = File(...),
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_active_user)
):
    property = crud.get_property(db, property_id=property_id)
    if not property:
        raise HTTPException(
            status_code = status.HTTP_404_NOT_FOUND,
            detail = "Property not found"
        )
    if property.owner_id != current_user.id: # type: ignore
        raise HTTPException(
            status_code = status.HTTP_403_FORBIDDEN,
            detail = "You are not the owner of this property"
        )
    
    image_urls = []
    for image in images:
        file_ext = os.path.splitext(image.filename)[1] # type: ignore
        filename = f"{uuid.uuid4()}{file_ect}"# type: ignore
        file_path = f"app/static/images/properties/{filename}"
        with open(file_path, "wb") as buffer:
            buffer.write(await image.read())
            
        image_url = f"/static/images/properties/{filename}"
        image_urls.append(image_url)
        
    return crud.app_property_images(db, property_id=property_id, image_urls=image_urls) # type: ignore
        

@router.put("/{property_id}/status")
async def update_property_status(
    property_id: int,
    status: str,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_active_user)
):
    property = crud.get_property(db, property_id = property_id)
    if not property:
        raise HTTPException(
            status_code = status.HTTP_404_NOT_FOUND, # type: ignore
            detail = "Property not found"
        )
    if property.owner_id != current_user.id: # pyright: ignore[reportAttributeAccessIssue]
        raise HTTPException(
            status_code = status.HTTP_403_FORBIDDEN, # type: ignore
            detail = "You are not the owner of this property"
        )
    return crud.update_property_status(db, property_id=property_id, status=status) # type: ignore