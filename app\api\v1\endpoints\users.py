from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from app.db import crud
from app.db.session import get_db
from app.schemas.users import UserInDB, UserUpdate
from app.core.security import get_current_active_user
from typing import Optional

router = APIRouter()

@router.get("/me", response_model=UserInDB)
async def read_user_me(
    current_user: UserInDB = Depends(get_current_active_user)
):
    return current_user

@router.put("/me", response_model=UserInDB)
async def update_user_me(
    user_update: UserUpdate,
    db: Session = Depends(get_db),
    current_user: UserInDB = Depends(get_current_active_user)
):
    updated_user = crud.update_user(db, user_id=current_user.id, user_update=user_update) # type: ignore
    if not updated_user:
        raise HTTPException(status_code=404, detail="User not found")
    return updated_user

@router.post("/favorites/{property_id}")
async def add_to_favorites(
    property_id: int,
    db: Session = Depends(get_db),
    current_user: UserInDB = Depends(get_current_active_user)
):
    # Check if property exists
    property = crud.get_property(db, property_id=property_id)
    if not property:
        raise HTTPException(status_code=404, detail="Property not found")
    
    return crud.add_favorite(db, user_id=current_user.id, property_id=property_id)

@router.delete("/favorites/{property_id}")
async def remove_from_favorites(
    property_id: int,
    db: Session = Depends(get_db),
    current_user: UserInDB = Depends(get_current_active_user)
):
    success = crud.remove_favorite(db, user_id=current_user.id, property_id=property_id)
    if not success:
        raise HTTPException(status_code=404, detail="Favorite not found")
    return {"message": "Removed from favorites"}

@router.get("/notifications")
async def get_notifications(
    is_read: Optional[bool] = None,
    db: Session = Depends(get_db),
    current_user: UserInDB = Depends(get_current_active_user)
):
    return crud.get_user_notifications(db, user_id=current_user.id, is_read=is_read) # type: ignore

@router.post("/notifications/{notification_id}/read")
async def mark_notification_as_read(
    notification_id: int,
    db: Session = Depends(get_db),
    current_user: UserInDB = Depends(get_current_active_user)
):
    notification = crud.mark_notification_as_read(db, notification_id=notification_id)
    if not notification:
        raise HTTPException(status_code=404, detail="Notification not found")
    return notification


