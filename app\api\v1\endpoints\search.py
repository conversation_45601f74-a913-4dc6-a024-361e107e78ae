from fastapi import APIRouter, Depends, Query, HTTPException
from sqlalchemy.orm import Session
from typing import Optional, List
from app.db import crud
from app.db.session import get_db
from app.core.security import get_current_user
from app.core.ai_services import ai_service
from app.schemas.properties import PropertyOut
from app.utils.notifications import create_notification
import logging

logger = logging.getLogger(__name__)

router = APIRouter()

@router.get("/", response_model=dict)
async def search_properties(
    query: Optional[str] = None,
    price_min: Optional[float] = Query(None, ge=0),
    price_max: Optional[float] = Query(None, ge=0),
    bhk: Optional[int] = Query(None, ge=1),
    property_type: Optional[str] = None,
    furnishing: Optional[str] = None,
    location: Optional[str] = None,
    verified_owner: Optional[bool] = None,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: Optional[dict] = Depends(get_current_user)
):
    """
    AI-powered property search with intelligent filtering and recommendations
    """
    try:
        filters = {
            "price_min": price_min,
            "price_max": price_max,
            "bhk": bhk,
            "property_type": property_type,
            "furnishing": furnishing,
            "location": location,
            "verified_owner": verified_owner,
        }

        # Get base properties using filters
        properties = crud.get_properties(db, skip=skip, limit=limit, filters=filters)

        # Apply text search if query is provided
        if query:
            # Enhanced search with AI-like scoring
            scored_properties = []
            query_lower = query.lower()

            for prop in properties:
                score = 0
                title_lower = getattr(prop, 'title', '').lower()
                desc_lower = getattr(prop, 'description', '').lower()
                city_lower = getattr(prop, 'city', '').lower()

                # Title match gets highest score
                if query_lower in title_lower:
                    score += 10
                # Description match gets medium score
                if query_lower in desc_lower:
                    score += 5
                # Location match gets medium score
                if query_lower in city_lower:
                    score += 7

                # Word-based matching for better results
                query_words = query_lower.split()
                for word in query_words:
                    if word in title_lower:
                        score += 3
                    if word in desc_lower:
                        score += 2
                    if word in city_lower:
                        score += 2

                if score > 0:
                    scored_properties.append((prop, score))

            # Sort by score and extract properties
            scored_properties.sort(key=lambda x: x[1], reverse=True)
            properties = [prop for prop, score in scored_properties]

        # Get AI-powered recommendations if user is authenticated
        recommendations = []
        if current_user and properties:
            try:
                # Get similar properties for the first few results
                for prop in properties[:3]:
                    similar = ai_service.get_similar_properties(
                        db,
                        getattr(prop, 'id'),
                        top_n=2
                    )
                    recommendations.extend([{
                        "property_id": rec.property_id,
                        "score": rec.score,
                        "reasons": rec.reasons,
                        "based_on": getattr(prop, 'id')
                    } for rec in similar])
            except Exception as e:
                logger.warning(f"Failed to get AI recommendations: {str(e)}")

        # Convert properties to response format
        property_results = []
        for prop in properties:
            try:
                property_results.append(PropertyOut.model_validate(prop))
            except Exception as e:
                logger.warning(f"Failed to validate property {getattr(prop, 'id', 'unknown')}: {str(e)}")

        return {
            "properties": property_results,
            "total": len(property_results),
            "recommendations": recommendations[:5],  # Limit recommendations
            "search_query": query,
            "filters_applied": {k: v for k, v in filters.items() if v is not None},
            "has_more": len(properties) == limit
        }

    except Exception as e:
        logger.error(f"Error in property search: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Search failed. Please try again."
        )

@router.get("/smart-suggestions")
async def get_smart_search_suggestions(
    query: str,
    db: Session = Depends(get_db),
    current_user: Optional[dict] = Depends(get_current_user)
):
    """
    Get AI-powered search suggestions based on user query
    """
    try:
        # Get all properties for analysis
        all_properties = crud.get_properties(db, skip=0, limit=1000)

        suggestions = {
            "locations": set(),
            "property_types": set(),
            "price_ranges": [],
            "similar_searches": []
        }

        query_lower = query.lower()

        # Extract suggestions from existing properties
        for prop in all_properties:
            city = getattr(prop, 'city', '').lower()
            prop_type = getattr(prop, 'property_type', '')
            title = getattr(prop, 'title', '').lower()

            # Location suggestions
            if query_lower in city:
                suggestions["locations"].add(getattr(prop, 'city', ''))

            # Property type suggestions
            if query_lower in str(prop_type).lower():
                suggestions["property_types"].add(str(prop_type))

            # Similar search suggestions based on title
            if any(word in title for word in query_lower.split()):
                suggestions["similar_searches"].append(getattr(prop, 'title', ''))

        # Convert sets to lists and limit results
        return {
            "query": query,
            "suggestions": {
                "locations": list(suggestions["locations"])[:10],
                "property_types": list(suggestions["property_types"])[:5],
                "similar_searches": suggestions["similar_searches"][:10]
            }
        }

    except Exception as e:
        logger.error(f"Error getting search suggestions: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Failed to get search suggestions"
        )