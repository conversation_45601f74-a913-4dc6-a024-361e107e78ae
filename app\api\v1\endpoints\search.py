from fastapi import APIRouter, Depends, Query
from sqlalchemy.orm import Session
from typing import Optional
from app.db import crud
from app.db.session import get_db
from app.core.security import get_current_user
from app.schemas.properties import PropertyOut

router = APIRouter()

@router.get("/", response_model=list[PropertyOut])
async def search_properties(
    query: Optional[str] = None,
    price_min: Optional[float] = Query(None, ge=0),
    price_max: Optional[float] = Query(None, ge=0),
    bhk: Optional[int] = Query(None, ge=1),
    property_type: Optional[str] = None,
    furnishing: Optional[str] = None,
    location: Optional[str] = None,
    verified_owner: Optional[bool] = None,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    filters = {
        "price_min": price_min,
        "price_max": price_max,
        "bhk": bhk,
        "property_type": property_type,
        "furnishing": furnishing,
        "location": location,
        "verified_owner": verified_owner,
    }
    
    if query:
        # Basic search implementation - would be enhanced with full-text search in production
        properties = crud.get_properties(db, skip=skip, limit=limit, filters=filters)
        return [p for p in properties if query.lower() in p.title.lower() or query.lower() in p.description.lower()]
    
    return crud.get_properties(db, skip=skip, limit=limit, filters=filters)