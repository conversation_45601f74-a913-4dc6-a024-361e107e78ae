from fastapi import APIRout<PERSON>, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
from app.core import security
from app.core.firebase import verify_token, get_firebase_user
from app.db.session import get_db
from app.db import crud
from app.schemas.users import UserInDB
import firebase_admin.auth as firebase_auth

router = APIRouter()

@router.post("/login")
async def login_with_firebase(
    id_token: str,
    db: Session = Depends(get_db)
):
    try:
        decoded_token = await verify_token(id_token)
        firebase_uid = decoded_token["uid"]
        
        db_user = crud.get_user_by_firebase_uid(db, firebase_uid)
        if not db_user:
            
            firebase_user = await get_firebase_user(firebase_uid)
            user_data = {
                "firebase_uid": firebase_uid,
                "email": firebase_user.email,
                "name": firebase_user.display_name or "",
                "phone":firebase_user.phone_number or "",
                "role":"tenant"
            
            }
            
            db_user = crud.create_user(db, user_data)
        
        return{
            "access_token": id_token,
            "token_type": "bearer",
            "user": UserInDB.from_orm(db_user)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="invalid authentication credentials", headers={"WWW-Authenticate": "Bearer"})

@router.post("/kyc", dependencies=[Depends(security.get_current_active_user)])
async def submit_kyc(
    kyc_details: dict,
    db: Session = Depends(get_db),
    current_user: dict = Depends(security.get_current_active_user)
):
    return crud.update_user_kyc(db, user_id = current_user.id, kyc_details=kyc_details) # type: ignore
