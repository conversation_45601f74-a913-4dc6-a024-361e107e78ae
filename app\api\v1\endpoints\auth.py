from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from sqlalchemy.orm import Session
from app.core import security
from app.core.firebase import verify_token, get_firebase_user
from app.core.ai_services import ai_service
from app.db.session import get_db
from app.db import crud
from app.schemas.users import UserInDB, UserRegistration, UserRegistrationResponse
from app.utils.notifications import create_notification
from typing import Optional
import logging

logger = logging.getLogger(__name__)

router = APIRouter()

@router.post("/login")
async def login_with_firebase(
    id_token: str,
    db: Session = Depends(get_db)
):
    try:
        decoded_token = await verify_token(id_token)
        firebase_uid = decoded_token["uid"]
        
        db_user = crud.get_user_by_firebase_uid(db, firebase_uid)
        if not db_user:
            
            firebase_user = await get_firebase_user(firebase_uid)
            user_data = {
                "firebase_uid": firebase_uid,
                "email": firebase_user.email,
                "name": firebase_user.display_name or "",
                "phone":firebase_user.phone_number or "",
                "role":"tenant"
            
            }
            
            db_user = crud.create_user(db, user_data)
        
        return{
            "access_token": id_token,
            "token_type": "bearer",
            "user": UserInDB.model_validate(db_user)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="invalid authentication credentials", headers={"WWW-Authenticate": "Bearer"})

@router.post("/register", response_model=UserRegistrationResponse)
async def register_user(
    user_data: UserRegistration,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """
    Register a new user with AI-powered fraud detection and personalized recommendations
    """
    try:
        # Check if user already exists
        existing_user = crud.get_user_by_email(db, user_data.email)
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="User with this email already exists"
            )

        # Prepare user data for database
        user_dict = user_data.model_dump()
        preferences = user_dict.pop('preferences', {})
        location = user_dict.pop('location', None)

        # Create user in database
        db_user = crud.create_user(db, user_dict)

        # AI-powered fraud detection
        fraud_analysis = await ai_service.detect_fraud(
            property_data={},  # No property data for user registration
            user_data={
                "email": user_data.email,
                "phone": user_data.phone,
                "name": user_data.name,
                "location": location,
                "properties_posted": 0,
                "kyc_verified": False
            }
        )

        # Get personalized property recommendations if user is a tenant/buyer
        recommendations = []
        if user_data.role in ["tenant", "buyer"] and preferences:
            try:
                # Get initial recommendations based on preferences
                properties = crud.get_properties(db, skip=0, limit=10, filters=preferences)
                if properties:
                    # Use AI service to get similar properties
                    for prop in properties[:3]:  # Get top 3 properties
                        try:
                            similar = ai_service.get_similar_properties(db, getattr(prop, 'id'), top_n=2)
                            recommendations.extend([{
                                "property_id": rec.property_id,
                                "score": rec.score,
                                "reasons": rec.reasons
                            } for rec in similar])
                        except Exception as prop_error:
                            logger.warning(f"Failed to get similar properties for {getattr(prop, 'id', 'unknown')}: {str(prop_error)}")
            except Exception as e:
                logger.warning(f"Failed to generate recommendations for user {getattr(db_user, 'id', 'unknown')}: {str(e)}")

        # Create welcome notification
        await create_notification(
            db=db,
            user_id=getattr(db_user, 'id'),
            title="Welcome to DreamBig!",
            message="Your account has been created successfully. Start exploring properties now!",
            notification_type="welcome"
        )

        # Log registration for analytics
        logger.info(f"New user registered: {db_user.id}, email: {user_data.email}, role: {user_data.role}")

        return UserRegistrationResponse(
            user=UserInDB.model_validate(db_user),
            recommendations=recommendations[:5],  # Limit to top 5
            fraud_score=fraud_analysis
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error during user registration: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Registration failed. Please try again."
        )

@router.post("/kyc", dependencies=[Depends(security.get_current_active_user)])
async def submit_kyc(
    kyc_details: dict,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: dict = Depends(security.get_current_active_user)
):
    """
    Submit KYC details with AI-powered fraud detection
    """
    try:
        # AI-powered fraud detection for KYC
        fraud_analysis = await ai_service.detect_fraud(
            property_data={},
            user_data={
                "email": getattr(current_user, 'email', ''),
                "phone": getattr(current_user, 'phone', ''),
                "name": getattr(current_user, 'name', ''),
                "kyc_details": kyc_details,
                "properties_posted": len(crud.get_properties_by_owners(db, getattr(current_user, 'id'), "available")),
                "kyc_verified": False
            }
        )

        # If fraud score is too high, reject KYC
        if fraud_analysis.get("is_fraud", False) and fraud_analysis.get("confidence", 0) > 0.7:
            await create_notification(
                db=db,
                user_id=getattr(current_user, 'id'),
                title="KYC Verification Failed",
                message="Your KYC submission requires manual review due to security concerns.",
                notification_type="kyc_failed"
            )
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="KYC verification failed. Please contact support."
            )

        # Update user KYC
        updated_user = crud.update_user_kyc(db, user_id=getattr(current_user, 'id'), kyc_details=kyc_details)

        # Create success notification
        await create_notification(
            db=db,
            user_id=getattr(current_user, 'id'),
            title="KYC Verified Successfully",
            message="Your identity has been verified. You can now access all features.",
            notification_type="kyc_success"
        )

        return {
            "user": UserInDB.model_validate(updated_user),
            "fraud_analysis": fraud_analysis,
            "message": "KYC submitted successfully"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error during KYC submission: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="KYC submission failed. Please try again."
        )
