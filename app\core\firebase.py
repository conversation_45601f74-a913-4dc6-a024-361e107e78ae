import firebase_admin
from firebase_admin import credentials,auth
from fastapi import HTT<PERSON>Ex<PERSON>, status
from app.config import settings 

def intialize_firebase():
    try:
        cred = credentials.Certificate(settings. FIREBASE_CREDENTIALS)
        firebase_admin.initialize_app(cred)
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))
    

async def verify_token(id_token: str) -> dict:
    try:
        decoded_token = auth.verify_id_token(id_token)
        return decoded_token
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail=str(e), headers={"WWW-Authenticate": "Bearer"})
    
    except Exception as e: # type: ignore
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail = 'Error veriying Firebase ID token',
            headers ={"WWW-Authenticate": "Bearer"},
        )
    
async def get_firebase_user(uid: str):
    try:
        return auth.get_user(uid)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Error fetching Firebase user: {str(e)}"
        )