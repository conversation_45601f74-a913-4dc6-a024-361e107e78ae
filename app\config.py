from pydantic import BaseSettings
from typing import List, Optional
import os
from dotenv import load_dotenv
load_dotenv()

class Settings(BaseSettings): # type: ignore
    PROJECT_NAME: str = "DreamBig"
    API_V1_STR: str = "/api/v1"
    
    
    SQLALCHEMY_DATABASE_URI: Optional[str] = os.getenv("DATABASE_URL")
    
    FIREBASE_CREDENTIALS: str = r"D://firebase//app//dreambig_firebase_credentioal.json"
    
    
    CORS_ORIGINS: List[str] = ["*"]
    
    SMTP_HOST: Optional[str] = None
    SMTP_PORT: Optional[int] = None
    SMTP_USER: Optional[str] = None
    SMTP_PASSWORD: Optional[str] = None
    EMAILS_FROM_EMAIL: Optional[str] = None
    
    
    class Config:
        env_file = ".env"
        
settings = Settings()

    