from pydantic import BaseModel, EmailStr, Field
from typing import Optional
from datetime import datetime
from app.db.models import UserRole

class UserBase(BaseModel):
    email: EmailStr
    phone: str = Field(..., min_length=10, max_length=15)
    name: str = Field(..., min_length=2, max_length=100)

class UserCreate(UserBase):
    firebase_uid: str = Field(..., min_length=20)
    role: UserRole = UserRole.TENANT

class UserUpdate(BaseModel):
    phone: Optional[str] = Field(None, min_length=10, max_length=15)
    name: Optional[str] = Field(None, min_length=2, max_length=100)

class UserInDB(UserBase):
    id: int
    firebase_uid: str
    role: UserRole
    is_active: bool
    kyc_verified: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True