from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>umn, <PERSON><PERSON><PERSON>, Integer, String, Float, DateTime, JSON, Enum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.db.session import Base
import enum

class UserRole(str, enum.Enum):
    TENANT = "tenant"
    OWNER = "owner"
    SERVICE_PROVIDER = "service_provider"
    INVESTOR = "investor"
    ADMIN = "admin"

class PropertyType(str, enum.Enum):
    APARTMENT = "apartment"
    HOUSE = "house"
    VILLA = "villa"
    PLOT = "plot"
    COMMERCIAL = "commercial"

class FurnishingType(str, enum.Enum):
    FURNISHED = "furnished"
    SEMI_FURNISHED = "semi_furnished"
    UNFURNISHED = "unfurnished"

class PropertyStatus(str, enum.Enum):
    AVAILABLE = "available"
    PENDING = "pending"
    RENTED = "rented"
    SOLD = "sold"

class InvestmentRiskLevel(str, enum.Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"

class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    firebase_uid = Column(String, unique=True, index=True)
    email = Column(String, unique=True, index=True)
    phone = Column(String, unique=True, index=True)
    name = Column(String)
    role = Column(Enum(UserRole), default=UserRole.TENANT)
    is_active = Column(Boolean, default=True)
    kyc_verified = Column(Boolean, default=False)
    kyc_details = Column(JSON)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    properties = relationship("Property", back_populates="owner")
    favorites = relationship("Favorite", back_populates="user")
    recently_viewed = relationship("RecentlyViewed", back_populates="user")
    investments = relationship("Investment", back_populates="investor")
    service_bookings = relationship("ServiceBooking", back_populates="user")
    notifications = relationship("Notification", back_populates="user")

class Property(Base):
    __tablename__ = "properties"

    id = Column(Integer, primary_key=True, index=True)
    title = Column(String)
    description = Column(String)
    price = Column(Float)
    bhk = Column(Integer)
    area = Column(Float)
    property_type = Column(Enum(PropertyType))
    furnishing = Column(Enum(FurnishingType))
    address = Column(String)
    city = Column(String)
    state = Column(String)
    pincode = Column(String)
    latitude = Column(Float)
    longitude = Column(Float)
    status = Column(Enum(PropertyStatus), default=PropertyStatus.AVAILABLE)
    is_verified = Column(Boolean, default=False)
    owner_id = Column(Integer, ForeignKey("users.id"))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    owner = relationship("User", back_populates="properties")
    images = relationship("PropertyImage", back_populates="property")
    features = relationship("PropertyFeature", back_populates="property")
    favorites = relationship("Favorite", back_populates="property")
    recently_viewed = relationship("RecentlyViewed", back_populates="property")
    investments = relationship("Investment", back_populates="property")

class PropertyImage(Base):
    __tablename__ = "property_images"

    id = Column(Integer, primary_key=True, index=True)
    url = Column(String)
    property_id = Column(Integer, ForeignKey("properties.id"))

    property = relationship("Property", back_populates="images")

class PropertyFeature(Base):
    __tablename__ = "property_features"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String)
    value = Column(String)
    property_id = Column(Integer, ForeignKey("properties.id"))

    property = relationship("Property", back_populates="features")

class Favorite(Base):
    __tablename__ = "favorites"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    property_id = Column(Integer, ForeignKey("properties.id"))
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    user = relationship("User", back_populates="favorites")
    property = relationship("Property", back_populates="favorites")

class RecentlyViewed(Base):
    __tablename__ = "recently_viewed"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    property_id = Column(Integer, ForeignKey("properties.id"))
    viewed_at = Column(DateTime(timezone=True), server_default=func.now())

    user = relationship("User", back_populates="recently_viewed")
    property = relationship("Property", back_populates="recently_viewed")

class Investment(Base):
    __tablename__ = "investments"

    id = Column(Integer, primary_key=True, index=True)
    amount = Column(Float)
    expected_roi = Column(Float)
    risk_level = Column(Enum(InvestmentRiskLevel))
    start_date = Column(DateTime(timezone=True))
    end_date = Column(DateTime(timezone=True))
    status = Column(String)
    investor_id = Column(Integer, ForeignKey("users.id"))
    property_id = Column(Integer, ForeignKey("properties.id"))
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    investor = relationship("User", back_populates="investments")
    property = relationship("Property", back_populates="investments")
    documents = relationship("InvestmentDocument", back_populates="investment")

class InvestmentDocument(Base):
    __tablename__ = "investment_documents"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String)
    url = Column(String)
    investment_id = Column(Integer, ForeignKey("investments.id"))

    investment = relationship("Investment", back_populates="documents")

class ServiceProvider(Base):
    __tablename__ = "service_providers"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String)
    service_type = Column(String)
    description = Column(String)
    contact_number = Column(String)
    email = Column(String)
    is_verified = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    service_bookings = relationship("ServiceBooking", back_populates="service_provider")

class ServiceBooking(Base):
    __tablename__ = "service_bookings"

    id = Column(Integer, primary_key=True, index=True)
    service_type = Column(String)
    details = Column(JSON)
    status = Column(String)
    user_id = Column(Integer, ForeignKey("users.id"))
    service_provider_id = Column(Integer, ForeignKey("service_providers.id"))
    property_id = Column(Integer, ForeignKey("properties.id"), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    user = relationship("User", back_populates="service_bookings")
    service_provider = relationship("ServiceProvider", back_populates="service_bookings")
    property = relationship("Property")

class Notification(Base):
    __tablename__ = "notifications"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    title = Column(String)
    message = Column(String)
    is_read = Column(Boolean, default=False)
    type = Column(String)
    reference_id = Column(Integer)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    user = relationship("User", back_populates="notifications")